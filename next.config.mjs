import {withSentryConfig} from '@sentry/nextjs';
import withBundleAnalyzer from '@next/bundle-analyzer';

/** @type {import('next').NextConfig} */
const nextConfig = {
  eslint: {
    // Warning: This allows production builds to successfully complete even if
    // your project has ESLint errors.
    ignoreDuringBuilds: true,
  },
  reactStrictMode: true,
  // Add other configurations here as needed
  // e.g., experimental features, redirects, rewrites, etc.

  // Exclude specific directories from build
  pageExtensions: ['js', 'jsx', 'ts', 'tsx'],
  webpack: (config, { isServer, dev }) => {
    // Exclude src, src_old, backup, and src_older_backup directories
    config.module.rules.push({
      test: /\.(js|jsx|ts|tsx)$/,
      exclude: /src|backup|src_old|src_older_backup/,
      use: []
    });

    // Fix for MongoDB Node.js modules in client components
    if (!isServer) {
      config.resolve.fallback = {
        ...config.resolve.fallback,
        fs: false,
        net: false,
        tls: false,
        dns: false,
        child_process: false,
        'fs/promises': false,
        'timers/promises': false,
      };
      
      // Add custom handling for HMR in development to fix "Unrecognized HMR message" errors
      if (dev) {
        // Suppress the HMR ping error
        const DefinePlugin = config.plugins.find(
          (plugin) => plugin.constructor.name === 'DefinePlugin'
        );
        
        if (DefinePlugin) {
          // Add a global flag to silence the HMR ping error
          DefinePlugin.definitions['process.env.NEXT_SUPPRESS_HMR_ERROR'] = JSON.stringify(true);
        }
        
        // Modify the hot middleware client to ignore the ping messages
        const originalEntry = config.entry;
        config.entry = async () => {
          const entries = await (typeof originalEntry === 'function' ? originalEntry() : originalEntry);
          
          // Find and modify the client-side entry points
          Object.keys(entries).forEach((entry) => {
            if (entries[entry] && Array.isArray(entries[entry])) {
              // Look for the webpack-hot-middleware client entry
              const hmrIndex = entries[entry].findIndex((file) => 
                file.includes('webpack-hot-middleware')
              );
              
              if (hmrIndex !== -1) {
                console.log(`[HMR Fix] Applying custom HMR client config for entry: ${entry}`);
                // Replace with our customized HMR client that ignores ping messages
                entries[entry][hmrIndex] = entries[entry][hmrIndex] + '?quiet=true&overlay=false';
              }
            }
          });
          
          return entries;
        };
      }
    }

    return config;
  },

  // Optimize HMR in development mode
  experimental: {
    // Improve HMR reliability
    optimizeServerReact: true,
    // Disable automatic static optimization for more reliable HMR
    disableOptimizedLoading: process.env.NODE_ENV === 'development',
    // Moved trace config to avoid warnings
  },

  // Next.js 15+ requires different Sentry configuration
  // These options will be passed to the Sentry plugin
  // Enable Sentry integration is now handled by the withSentryConfig wrapper
};

// Enable bundle analyzer in development when ANALYZE is set
const analyzeBundleEnabled = process.env.ANALYZE === 'true';
const bundleAnalyzer = withBundleAnalyzer({
  enabled: analyzeBundleEnabled,
});

// Sentry configuration
export default bundleAnalyzer(withSentryConfig(nextConfig, {
// For all available options, see:
// https://www.npmjs.com/package/@sentry/webpack-plugin#options

org: "trendtech-innovations",
project: "ims-tej",

// Only print logs for uploading source maps in CI
silent: !process.env.CI,

// For all available options, see:
// https://docs.sentry.io/platforms/javascript/guides/nextjs/manual-setup/

// Upload a larger set of source maps for prettier stack traces (increases build time)
widenClientFileUpload: true,

// Route browser requests to Sentry through a Next.js rewrite to circumvent ad-blockers.
// This can increase your server load as well as your hosting bill.
// Note: Check that the configured route will not match with your Next.js middleware, otherwise reporting of client-
// side errors will fail.
tunnelRoute: "/monitoring",

// Automatically tree-shake Sentry logger statements to reduce bundle size
disableLogger: true,

// Trace configuration (moved from experimental)
trace: false,

// Include source maps for better error reporting
sourcemaps: {
  // Include source maps in development for better debugging
  include: ['./'],
  // Don't ignore any source maps
  ignore: [],
},

// Enables automatic instrumentation of Vercel Cron Monitors.
automaticVercelMonitors: true,
}));