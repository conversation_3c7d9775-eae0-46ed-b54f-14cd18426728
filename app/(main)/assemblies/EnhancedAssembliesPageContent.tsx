'use client';

import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { SlidersHorizontal, Plus, LayoutGrid, List, Clock, Layers } from 'lucide-react';
import Link from 'next/link';

import Header from '@/app/components/layout/Header';
import { EnhancedBackground } from '@/app/components/layout/EnhancedBackground';
import AssembliesTableClient from '@/app/components/tables/AssembliesTable/AssembliesTableClient';
import { AssembliesGrid } from '@/app/components/tables/AssembliesGrid';
import { Button } from '@/app/components/ui/button';
import { useTheme } from '@/app/context/ThemeContext';
import { Card, CardContent, CardHeader, CardTitle } from '@/app/components/ui/card';
import { Input } from '@/app/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/app/components/ui/select';
import { RefreshDataButton } from '@/app/components/actions/RefreshDataButton';
import { AutoRefreshControl } from '@/app/components/controls/AutoRefreshControl';
import { useAssemblies } from '@/app/contexts/AssembliesContext';
import EnhancedAssemblyCard from '@/app/components/cards/EnhancedAssemblyCard';
import { cn } from '@/app/lib/utils';
import { NewAssemblyButton } from '@/app/components/navigation/NewAssemblyButton';

// SCHEMA ALIGNMENT: Updated all mapping, filtering, and display logic to use canonical assembly schema from database_schema_updated.md. Legacy/incorrect fields removed or migrated.
// Use only canonical fields: assemblyCode, name, partsRequired: [{ partId, quantityRequired, unitOfMeasure }], etc.
// Remove all legacy/incorrect fields and update all usages and types accordingly.

/**
 * Enhanced Assemblies page content component with Magic UI effects
 */
const EnhancedAssembliesPageContent: React.FC = () => {
    const { theme } = useTheme();
    const {
        assemblies,
        isLoading,
        error,
        refreshAssemblies
    } = useAssemblies();

    const [showFilters, setShowFilters] = useState(false);
    const [searchQuery, setSearchQuery] = useState('');
    const [statusFilter, setStatusFilter] = useState<'all' | 'active' | 'draft' | 'archived'>('all');
    const [sortBy, setSortBy] = useState<string>('name'); // Explicit type annotation
    const [viewMode, setViewMode] = useState<'grid' | 'table'>('grid'); // Default to grid view
    const [lastUpdated, setLastUpdated] = useState<Date | null>(null); // Track when data was last updated

    // Fetch assemblies with parts data for both grid and table views
    useEffect(() => {
        const fetchAssemblies = async () => {
            try {
                // Always fetch with parts data for both grid and table views
                await refreshAssemblies({ includeParts: true });
                setLastUpdated(new Date()); // Update the lastUpdated timestamp whenever data is refreshed
            } catch (error) {
                console.error("Failed to refresh assemblies:", error);
            }
        };
        fetchAssemblies();
    }, [viewMode]); // Remove refreshAssemblies dependency to prevent infinite loop

    // Re-fetch with parts data when changing view mode
    const handleViewModeChange = (mode: 'grid' | 'table') => {
        setViewMode(mode);
        if (mode === 'grid') {
            // When switching to grid view, fetch with parts data
            refreshAssemblies({ includeParts: true });
            setLastUpdated(new Date()); // Update timestamp when manually refreshing
        }
    };

    // Helper function to determine assembly status based on partsRequired
    const getAssemblyStatus = (assembly: any): 'complete' | 'incomplete' | 'review' => {
        // Safely handle potentially missing partsRequired field
        const hasValidParts = assembly.partsRequired && Array.isArray(assembly.partsRequired) && assembly.partsRequired.length > 0;

        if (!hasValidParts) {
            return 'incomplete';
        }

        // Check for missing partId references within partsRequired
        const hasMissingRefs = assembly.partsRequired.some((p: any) => !p.partId);
        if (hasMissingRefs) {
            return 'review';
        }

        return 'complete';
    };

    // Filter assemblies based on search query and status filter
    const filteredAssemblies = assemblies
        .filter(assembly => {
            // Skip invalid assemblies - use assemblyCode from new schema
            if (!assembly || !assembly.name) {
                console.warn('[AssembliesPage] Filtering out invalid assembly:', assembly);
                return false;
            }

            // Search by name, assemblyCode (new schema), or _id
            const matchesSearch =
                !searchQuery ||
                assembly.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                (assembly.assemblyCode && assembly.assemblyCode.toLowerCase().includes(searchQuery.toLowerCase())) ||
                (assembly._id && assembly._id.toLowerCase().includes(searchQuery.toLowerCase()));

            // Filter by status (using the status field from schema)
            const matchesStatus = !statusFilter || statusFilter === 'all' || assembly.status === statusFilter;

            return matchesSearch && matchesStatus;
        })
        .sort((a, b) => {
            // Sort based on selected sort option
            switch (sortBy) {
                case 'name':
                    return a.name.localeCompare(b.name);
                case 'name_desc':
                    return b.name.localeCompare(a.name);
                case 'code':
                    return (a.assemblyCode || '').localeCompare(b.assemblyCode || '');
                case 'code_desc':
                    return (b.assemblyCode || '').localeCompare(a.assemblyCode || '');
                case 'status':
                    return (a.status || '').localeCompare(b.status || '');
                case 'status_desc':
                    return (b.status || '').localeCompare(a.status || '');
                case 'parts':
                    const aCount = a.partsRequired?.length || 0;
                    const bCount = b.partsRequired?.length || 0;
                    return aCount - bCount;
                case 'parts_desc':
                    const aCountDesc = a.partsRequired?.length || 0;
                    const bCountDesc = b.partsRequired?.length || 0;
                    return bCountDesc - aCountDesc;
                default:
                    return 0;
            }
        });

    // Render loading state
    if (isLoading && assemblies.length === 0) {
        return (
            <EnhancedBackground>
            <Header title= "Assemblies" />
            <div className="container mx-auto px-4 py-8" >
                <div className="flex justify-between items-center mb-6" >
                    <h2 className="text-2xl font-semibold" > Loading Assemblies...</h2>
                        </div>
                        < div className = "grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6" >
                        {
                            [...Array(8)].map((_, index) => (
                                <Card key= { index } className = "h-64 animate-pulse" >
                                <CardHeader className="pb-2" >
                            <div className="h-6 bg-muted rounded w-3/4" > </div>
                            < div className = "h-4 bg-muted rounded w-1/4 mt-2" > </div>
                            </CardHeader>
                            < CardContent >
                            <div className="h-4 bg-muted rounded w-1/2 mb-4" > </div>
                            < div className = "space-y-2" >
                            <div className="h-3 bg-muted rounded" > </div>
                            < div className = "h-3 bg-muted rounded" > </div>
                            < div className = "h-3 bg-muted rounded w-3/4" > </div>
                            </div>
                            </CardContent>
                            </Card>
                            ))
                        }
                            </div>
                            </div>
                            </EnhancedBackground>
    );
  }

// Render error state
if (error) {
    return (
        <EnhancedBackground>
        <Header title= "Assemblies" />
        <div className="container mx-auto px-4 py-8" >
            <Card className="border-red-300 dark:border-red-700" >
                <CardHeader>
                <CardTitle className="text-red-500" > Error Loading Assemblies </CardTitle>
                    </CardHeader>
                    < CardContent >
                    <p>{ error } </p>
                    < Button
    onClick = {() => refreshAssemblies()
}
className = "mt-4"
variant = "outline"
    >
    Retry
    </Button>
    </CardContent>
    </Card>
    </div>
    </EnhancedBackground>
    );
  }

return (
    <EnhancedBackground patternType= "grid" interactiveMode = { false }>
        <Header title="Assemblies" >
            <div className="flex items-center gap-2" >
                <AutoRefreshControl />
                < RefreshDataButton onRefresh = {() => { refreshAssemblies({ includeParts: true }); return Promise.resolve(); }} />
                    </div>
                    </Header>

                    < div className = "container mx-auto px-4 py-8" >
                        {/* Header with actions */ }
                        < div className = "flex flex-col md:flex-row justify-between items-start md:items-center mb-6 gap-4" >
                            <motion.h2
            initial={{ opacity: 0, y: -20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            className={cn(
              "text-2xl font-semibold",
              theme === 'light'
                ? "text-gray-800"
                : "theme-gradient-text"
            )}
          >
    Manage Assemblies
        </motion.h2>

        < div className = "flex flex-wrap items-center gap-2" >
            <Button
              variant="outline"
              size="sm"
              onClick={() => setShowFilters(!showFilters)}
              className="group relative overflow-hidden"
            >
              <SlidersHorizontal size={16} className="mr-2" />
              <span>Filters</span>
              <div className={cn(
                "absolute inset-0 translate-x-[-100%] group-hover:translate-x-[100%] transition-transform duration-300",
                "bg-gradient-to-r theme-gradient-bg"
              )}></div>
            </Button>

            < div className = "flex items-center border rounded-md overflow-hidden" >
                <Button
                variant={ viewMode === 'grid' ? "default" : "ghost" }
size = "sm"
onClick = {() => handleViewModeChange('grid')}
className = "rounded-none"
    >
    <LayoutGrid size={ 16 } />
        </Button>
        < Button
variant = { viewMode === 'table' ? "default" : "ghost"}
size = "sm"
onClick = {() => handleViewModeChange('table')}
className = "rounded-none"
    >
    <List size={ 16 } />
        </Button>
        </div>

        < NewAssemblyButton variant="magic" />
        </div>
        </div>

{/* Filters */ }
<AnimatedFilters show={ showFilters }>
    <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6" >
        <div>
        <label htmlFor="search" className = "text-sm font-medium block mb-1" >
            Search
            </label>
            < Input
id = "search"
placeholder = "Search by name or code..."
value = { searchQuery }
onChange = {(e) => setSearchQuery(e.target.value)}
className = "w-full"
    />
    </div>

    < div >
    <label htmlFor="status" className = "text-sm font-medium block mb-1" >
        Status
        </label>
        < Select value = { statusFilter } onValueChange = {(value) => setStatusFilter(value as 'all' | 'active' | 'draft' | 'archived')} >
            <SelectTrigger id="status" >
                <SelectValue placeholder="All Statuses" />
                    </SelectTrigger>
                    <SelectContent>
                    <SelectItem value="all">All Statuses</SelectItem>
                    <SelectItem value="active">Active</SelectItem>
                    <SelectItem value="draft">Draft</SelectItem>
                    <SelectItem value="archived">Archived</SelectItem>
                    </SelectContent>
                    </Select>
                    </div>

                                    < div >
                                    <label htmlFor="sort" className = "text-sm font-medium block mb-1" >
                                        Sort By
                                            </label>
                                            < Select value = { sortBy } onValueChange = { setSortBy } >
                                                <SelectTrigger id="sort" >
                                                    <SelectValue placeholder="Sort by..." />
                                                        </SelectTrigger>
                                                        < SelectContent >
                                                        <SelectItem value="name" > Name(A - Z) </SelectItem>
                                                            < SelectItem value = "name_desc" > Name(Z - A) </SelectItem>
                                                                < SelectItem value = "code" > Code(A - Z) </SelectItem>
                                                                    < SelectItem value = "code_desc" > Code(Z - A) </SelectItem>
                                                                        < SelectItem value = "status" > Status(A - Z) </SelectItem>
                                                                            < SelectItem value = "status_desc" > Status(Z - A) </SelectItem>
                                                                                < SelectItem value = "parts" > Parts(Low to High) </SelectItem>
                                                                                    < SelectItem value = "parts_desc" > Parts(High to Low) </SelectItem>
                                                                                        </SelectContent>
                                                                                        </Select>
                                                                                        </div>
                                                                                        </div>
                                                                                        </AnimatedFilters>

{/* No results state */ }
{
    filteredAssemblies.length === 0 ? (
        <Card className= "p-8 text-center" >
        <Layers className="h-12 w-12 mx-auto mb-4 text-gray-400" />
            <h3 className="text-lg font-medium mb-2" > No Assemblies Found </h3>
                < p className = "text-sm text-muted-foreground mb-4" >
                    { searchQuery || statusFilter
                    ? "No assemblies match your current filters. Try adjusting your search criteria."
                    : "There are no assemblies in the system yet."
}
</p>
{
    searchQuery || statusFilter ? (
        <Button variant= "outline" onClick = {() => {
        setSearchQuery('');
        setStatusFilter('all');
    }
}>
    Clear Filters
        </Button>
            ) : (
    <Button asChild >
    <Link href= "/assemblies/new" >
    <Plus size={ 16 } className = "mr-2" />
        Create Assembly
            </Link>
            </Button>
            )}
</Card>
        ) : (
    <>
    {/* Grid view */ }
            {
    viewMode === 'grid' && (
        <AssembliesGrid assemblies={filteredAssemblies} />
    )}

{/* Table view */ }
{
    viewMode === 'table' && (
        <Card>
        <CardContent className="p-0" >
            <AssembliesTableClient
                    assemblies={ filteredAssemblies }
                  />
        </CardContent>
        </Card>
            )
}

{/* Results count */ }
<div className="mt-4 text-sm text-muted-foreground flex items-center" >
    <Clock size={ 14 } className = "mr-1" />
        <span>Last updated: { lastUpdated ? new Date(lastUpdated).toLocaleTimeString() : 'Never' } </span>
            < span className = "mx-2" >•</span>
                < span > { filteredAssemblies.length } { filteredAssemblies.length === 1 ? 'assembly' : 'assemblies' } found </span>
                    </div>
                    </>
        )}
</div>
    </EnhancedBackground>
  );
};

// Animated filters component
interface AnimatedFiltersProps {
    children: React.ReactNode;
    show: boolean;
}

const AnimatedFilters: React.FC<AnimatedFiltersProps> = ({ children, show }) => {
    return (
        <motion.div
      initial= { false}
    animate = {{
        height: show ? 'auto' : 0,
            opacity: show ? 1 : 0,
                marginBottom: show ? 16 : 0,
      }
}
transition = {{ duration: 0.3 }}
className = "overflow-hidden"
    >
    { children }
    </motion.div>
  );
};

export default EnhancedAssembliesPageContent;
